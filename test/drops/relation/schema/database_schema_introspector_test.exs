defmodule Drops.Relation.Schema.DatabaseSchemaIntrospectorTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema.DatabaseSchemaIntrospector
  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{PrimaryKey, ForeignKey, Field, Indices}

  # Mock repository for testing
  defmodule MockRepo do
    def __adapter__, do: Ecto.Adapters.SQLite3

    def query("SELECT name, type\nFROM sqlite_master\nWHERE type IN ('table', 'view')\n  AND name NOT LIKE 'sqlite_%'\nORDER BY name\n") do
      {:ok, %{rows: [["users", "table"], ["posts", "table"], ["comments", "view"]]}}
    end

    def query("PRAGMA table_info(users)") do
      {:ok, %{
        rows: [
          [0, "id", "INTEGER", 1, nil, 1],
          [1, "name", "TEXT", 1, nil, 0],
          [2, "email", "TEXT", 0, nil, 0],
          [3, "created_at", "TIMESTAMP", 1, "CURRENT_TIMESTAMP", 0]
        ]
      }}
    end

    def query("PRAGMA table_info(posts)") do
      {:ok, %{
        rows: [
          [0, "id", "INTEGER", 1, nil, 1],
          [1, "title", "TEXT", 1, nil, 0],
          [2, "content", "TEXT", 0, nil, 0],
          [3, "user_id", "INTEGER", 1, nil, 0]
        ]
      }}
    end

    def query("PRAGMA foreign_key_list(posts)") do
      {:ok, %{rows: [[0, 0, "users", "user_id", "id", "NO ACTION", "NO ACTION", "NONE"]]}}
    end

    def query("PRAGMA foreign_key_list(users)") do
      {:ok, %{rows: []}}
    end

    def query("PRAGMA index_list(users)") do
      {:ok, %{rows: [["idx_users_email", 1, "c", 0, 0]]}}
    end

    def query("PRAGMA index_list(posts)") do
      {:ok, %{rows: []}}
    end

    def query("PRAGMA index_info(idx_users_email)") do
      {:ok, %{rows: [[0, 2, "email"]]}}
    end
  end

  describe "discover_tables/1" do
    test "discovers SQLite tables successfully" do
      assert {:ok, tables} = DatabaseSchemaIntrospector.discover_tables(MockRepo)

      assert length(tables) == 3
      assert %{name: "users", type: :table} in tables
      assert %{name: "posts", type: :table} in tables
      assert %{name: "comments", type: :view} in tables
    end

    test "returns error for unsupported adapter" do
      defmodule UnsupportedRepo do
        def __adapter__, do: Some.Unsupported.Adapter
      end

      assert {:error, {:unsupported_adapter, Some.Unsupported.Adapter}} =
               DatabaseSchemaIntrospector.discover_tables(UnsupportedRepo)
    end
  end

  describe "extract_table_schema/2" do
    test "extracts complete schema for SQLite table" do
      assert {:ok, schema} = DatabaseSchemaIntrospector.extract_table_schema(MockRepo, "users")

      assert schema.source == "users"
      assert schema.primary_key.fields == [:id]
      assert length(schema.fields) == 4

      # Check field details
      id_field = Enum.find(schema.fields, &(&1.name == :id))
      assert id_field.type == :integer
      assert id_field.ecto_type == :integer
      assert id_field.source == :id

      email_field = Enum.find(schema.fields, &(&1.name == :email))
      assert email_field.type == :string
      assert email_field.ecto_type == :string
      assert email_field.source == :email
    end

    test "extracts foreign keys for table with relationships" do
      assert {:ok, schema} = DatabaseSchemaIntrospector.extract_table_schema(MockRepo, "posts")

      assert length(schema.foreign_keys) == 1
      fk = List.first(schema.foreign_keys)
      assert fk.field == :user_id
      assert fk.references_table == "users"
      assert fk.references_field == :id
    end

    test "returns error for unsupported adapter" do
      defmodule UnsupportedRepo do
        def __adapter__, do: Some.Unsupported.Adapter
      end

      assert {:error, {:unsupported_adapter, Some.Unsupported.Adapter}} =
               DatabaseSchemaIntrospector.extract_table_schema(UnsupportedRepo, "users")
    end
  end

  describe "extract_all_schemas/2" do
    test "extracts schemas for all tables" do
      assert {:ok, results} = DatabaseSchemaIntrospector.extract_all_schemas(MockRepo)

      assert length(results) == 2  # Only tables, not views by default

      users_result = Enum.find(results, &(&1.table_name == "users"))
      assert users_result.schema.source == "users"
      assert users_result.extraction_errors == []

      posts_result = Enum.find(results, &(&1.table_name == "posts"))
      assert posts_result.schema.source == "posts"
      assert posts_result.extraction_errors == []
    end

    test "includes views when requested" do
      assert {:ok, results} = DatabaseSchemaIntrospector.extract_all_schemas(MockRepo, include_views: true)

      assert length(results) == 3
      table_names = Enum.map(results, & &1.table_name)
      assert "comments" in table_names
    end

    test "excludes specified tables" do
      assert {:ok, results} = DatabaseSchemaIntrospector.extract_all_schemas(MockRepo, exclude_tables: ["posts"])

      assert length(results) == 1
      assert List.first(results).table_name == "users"
    end
  end

  describe "normalize_database_type/1" do
    test "normalizes common database types" do
      assert DatabaseSchemaIntrospector.normalize_database_type("INTEGER") == :integer
      assert DatabaseSchemaIntrospector.normalize_database_type("TEXT") == :string
      assert DatabaseSchemaIntrospector.normalize_database_type("VARCHAR(255)") == :string
      assert DatabaseSchemaIntrospector.normalize_database_type("BOOLEAN") == :boolean
      assert DatabaseSchemaIntrospector.normalize_database_type("REAL") == :float
      assert DatabaseSchemaIntrospector.normalize_database_type("TIMESTAMP") == :naive_datetime
      assert DatabaseSchemaIntrospector.normalize_database_type("DATE") == :date
      assert DatabaseSchemaIntrospector.normalize_database_type("BLOB") == :binary
    end

    test "defaults to string for unknown types" do
      assert DatabaseSchemaIntrospector.normalize_database_type("UNKNOWN_TYPE") == :string
    end
  end

  describe "PostgreSQL support" do
    defmodule MockPostgresRepo do
      def __adapter__, do: Ecto.Adapters.Postgres

      def query("SELECT table_name, table_type\nFROM information_schema.tables\nWHERE table_schema = 'public'\n  AND table_type IN ('BASE TABLE', 'VIEW')\nORDER BY table_name\n") do
        {:ok, %{rows: [["users", "BASE TABLE"], ["posts", "BASE TABLE"]]}}
      end

      def query("SELECT\n  column_name,\n  data_type,\n  is_nullable,\n  column_default,\n  ordinal_position\nFROM information_schema.columns\nWHERE table_name = $1\n  AND table_schema = 'public'\nORDER BY ordinal_position\n", ["users"]) do
        {:ok, %{
          rows: [
            ["id", "integer", "NO", "nextval('users_id_seq'::regclass)", 1],
            ["name", "character varying", "NO", nil, 2],
            ["email", "character varying", "YES", nil, 3]
          ]
        }}
      end

      def query("SELECT a.attname\nFROM pg_index i\nJOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)\nWHERE i.indrelid = $1::regclass AND i.indisprimary\n", ["users"]) do
        {:ok, %{rows: [["id"]]}}
      end

      def query("SELECT\n  kcu.column_name,\n  ccu.table_name AS foreign_table_name,\n  ccu.column_name AS foreign_column_name\nFROM information_schema.table_constraints AS tc\nJOIN information_schema.key_column_usage AS kcu\n  ON tc.constraint_name = kcu.constraint_name\n  AND tc.table_schema = kcu.table_schema\nJOIN information_schema.constraint_column_usage AS ccu\n  ON ccu.constraint_name = tc.constraint_name\n  AND ccu.table_schema = tc.table_schema\nWHERE tc.constraint_type = 'FOREIGN KEY'\n  AND tc.table_name = $1\n", ["users"]) do
        {:ok, %{rows: []}}
      end

      def query("SELECT indexname, indexdef\nFROM pg_indexes\nWHERE tablename = $1\n  AND schemaname = 'public'\nORDER BY indexname\n", ["users"]) do
        {:ok, %{rows: []}}
      end
    end

    test "discovers PostgreSQL tables" do
      assert {:ok, tables} = DatabaseSchemaIntrospector.discover_tables(MockPostgresRepo)

      assert length(tables) == 2
      assert %{name: "users", type: :table} in tables
      assert %{name: "posts", type: :table} in tables
    end

    test "extracts PostgreSQL table schema" do
      assert {:ok, schema} = DatabaseSchemaIntrospector.extract_table_schema(MockPostgresRepo, "users")

      assert schema.source == "users"
      assert schema.primary_key.fields == [:id]
      assert length(schema.fields) == 3

      # Check field details
      id_field = Enum.find(schema.fields, &(&1.name == :id))
      assert id_field.type == :string  # PostgreSQL "integer" maps to :string in our simplified mapping
      assert id_field.source == :id
    end
  end
end
