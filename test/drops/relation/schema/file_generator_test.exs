defmodule Drops.Relation.Schema.FileGeneratorTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema.FileGenerator
  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{PrimaryKey, ForeignKey, Field, Indices, Index}

  # Helper function to create a test schema
  defp create_test_schema(source \\ "users") do
    primary_key = PrimaryKey.new([:id])
    
    fields = [
      Field.new(:id, :integer, :id, :id),
      Field.new(:name, :string, :string, :name),
      Field.new(:email, :string, :string, :email),
      Field.new(:created_at, :naive_datetime, :naive_datetime, :created_at)
    ]

    foreign_keys = [
      ForeignKey.new(:organization_id, "organizations", :id)
    ]

    indices = Indices.new([
      Index.new("idx_users_email", [:email], true, :btree)
    ])

    Schema.new(
      source,
      primary_key,
      foreign_keys,
      fields,
      indices,
      [],  # associations
      []   # virtual_fields
    )
  end

  describe "generate_schema_file/3" do
    test "generates basic schema file content" do
      schema = create_test_schema()
      module_name = "MyApp.Schemas.Users"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      # Check module definition
      assert content =~ "defmodule #{module_name} do"
      
      # Check moduledoc
      assert content =~ "Explicit schema definition for the 'users' table"
      
      # Check main schema function
      assert content =~ "def schema do"
      assert content =~ "Schema.new("
      assert content =~ "\"users\","
      
      # Check helper functions
      assert content =~ "defp primary_key do"
      assert content =~ "defp foreign_keys do"
      assert content =~ "defp fields do"
      assert content =~ "defp indices do"
      assert content =~ "defp associations do"
      assert content =~ "defp virtual_fields do"

      # Check usage examples
      assert content =~ "def usage_examples"
      assert content =~ "use Drops.Relation, schema:"
      assert content =~ "use Drops.Relation.Schema.Integration"
    end

    test "includes field definitions" do
      schema = create_test_schema()
      module_name = "MyApp.Schemas.Users"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      # Check field definitions
      assert content =~ "Field.new(:id, :integer, :id, :id)"
      assert content =~ "Field.new(:name, :string, :string, :name)"
      assert content =~ "Field.new(:email, :string, :string, :email)"
      assert content =~ "Field.new(:created_at, :naive_datetime, :naive_datetime, :created_at)"
    end

    test "includes primary key definition" do
      schema = create_test_schema()
      module_name = "MyApp.Schemas.Users"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      assert content =~ "PrimaryKey.new([:id])"
    end

    test "includes foreign key definitions" do
      schema = create_test_schema()
      module_name = "MyApp.Schemas.Users"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      assert content =~ "ForeignKey.new(:organization_id, \"organizations\", :id)"
    end

    test "includes index definitions" do
      schema = create_test_schema()
      module_name = "MyApp.Schemas.Users"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      assert content =~ "Index.new(\"idx_users_email\", [:email], true, :btree)"
    end

    test "handles empty foreign keys" do
      schema = %{create_test_schema() | foreign_keys: []}
      module_name = "MyApp.Schemas.Users"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      assert content =~ "defp foreign_keys do"
      assert content =~ "# Example:"
      assert content =~ "# ForeignKey.new(:user_id, \"users\", :id),"
    end

    test "handles empty indices" do
      schema = %{create_test_schema() | indices: Indices.new([])}
      module_name = "MyApp.Schemas.Users"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      assert content =~ "Indices.new([])"
    end
  end

  describe "generate_schema_files/3" do
    test "generates multiple schema files" do
      schemas = [
        create_test_schema("users"),
        create_test_schema("posts"),
        create_test_schema("comments")
      ]
      module_prefix = "MyApp.Schemas"

      assert {:ok, results} = FileGenerator.generate_schema_files(schemas, module_prefix)

      assert length(results) == 3

      # Check users result
      users_result = Enum.find(results, &(&1.table_name == "users"))
      assert users_result.module_name == "MyApp.Schemas.Users"
      assert users_result.file_path == "lib/my_app/schemas/users.ex"
      assert users_result.content =~ "defmodule MyApp.Schemas.Users do"

      # Check posts result
      posts_result = Enum.find(results, &(&1.table_name == "posts"))
      assert posts_result.module_name == "MyApp.Schemas.Posts"
      assert posts_result.file_path == "lib/my_app/schemas/posts.ex"
      assert posts_result.content =~ "defmodule MyApp.Schemas.Posts do"

      # Check comments result
      comments_result = Enum.find(results, &(&1.table_name == "comments"))
      assert comments_result.module_name == "MyApp.Schemas.Comments"
      assert comments_result.file_path == "lib/my_app/schemas/comments.ex"
      assert comments_result.content =~ "defmodule MyApp.Schemas.Comments do"
    end
  end

  describe "build_module_name/2" do
    test "converts table names to module names" do
      assert FileGenerator.build_module_name("MyApp.Schemas", "users") == "MyApp.Schemas.Users"
      assert FileGenerator.build_module_name("MyApp.Schemas", "user_profiles") == "MyApp.Schemas.UserProfiles"
      assert FileGenerator.build_module_name("MyApp.Schemas", "oauth_tokens") == "MyApp.Schemas.OauthTokens"
      assert FileGenerator.build_module_name("MyApp.Database.Schemas", "posts") == "MyApp.Database.Schemas.Posts"
    end
  end

  describe "build_file_path/2" do
    test "converts module names to file paths" do
      assert FileGenerator.build_file_path("MyApp.Schemas.Users", "lib") == "lib/my_app/schemas/users.ex"
      assert FileGenerator.build_file_path("MyApp.Schemas.UserProfiles", "lib") == "lib/my_app/schemas/user_profiles.ex"
      assert FileGenerator.build_file_path("MyApp.Database.Schemas.Posts", "lib") == "lib/my_app/database/schemas/posts.ex"
      assert FileGenerator.build_file_path("MyApp.Schemas.OauthTokens", "src") == "src/my_app/schemas/oauth_tokens.ex"
    end
  end

  describe "generated content validation" do
    test "generated content is valid Elixir code" do
      schema = create_test_schema()
      module_name = "MyApp.Schemas.Users"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      # Try to parse the generated content as Elixir code
      assert {:ok, _ast} = Code.string_to_quoted(content)
    end

    test "generated module can be compiled" do
      schema = create_test_schema()
      module_name = "TestGeneratedSchema"

      assert {:ok, content} = FileGenerator.generate_schema_file(schema, module_name)

      # Replace the module name to avoid conflicts
      content = String.replace(content, "MyApp.Schemas.Users", module_name)

      # Compile the generated code
      assert [{^module_name, _bytecode}] = Code.compile_string(content)

      # Check that the module has the expected functions
      assert function_exported?(TestGeneratedSchema, :schema, 0)
      assert function_exported?(TestGeneratedSchema, :usage_examples, 0)

      # Check that the schema function returns a valid schema
      schema_result = TestGeneratedSchema.schema()
      assert %Schema{} = schema_result
      assert schema_result.source == "users"
    end
  end

  describe "options handling" do
    test "respects include_comments option" do
      schema = create_test_schema()
      module_name = "MyApp.Schemas.Users"

      # With comments (default)
      assert {:ok, content_with_comments} = FileGenerator.generate_schema_file(schema, module_name, include_comments: true)
      assert content_with_comments =~ "# Primary key definition"
      assert content_with_comments =~ "# Field definitions"

      # Without comments
      assert {:ok, content_without_comments} = FileGenerator.generate_schema_file(schema, module_name, include_comments: false)
      refute content_without_comments =~ "# Primary key definition"
      refute content_without_comments =~ "# Field definitions"
    end

    test "respects include_associations_placeholder option" do
      schema = create_test_schema()
      module_name = "MyApp.Schemas.Users"

      # With associations placeholder (default)
      assert {:ok, content_with_placeholder} = FileGenerator.generate_schema_file(schema, module_name, include_associations_placeholder: true)
      assert content_with_placeholder =~ "Example associations:"
      assert content_with_placeholder =~ "%Ecto.Association.BelongsTo{"

      # Without associations placeholder
      assert {:ok, content_without_placeholder} = FileGenerator.generate_schema_file(schema, module_name, include_associations_placeholder: false)
      refute content_without_placeholder =~ "Example associations:"
      refute content_without_placeholder =~ "%Ecto.Association.BelongsTo{"
    end
  end
end
