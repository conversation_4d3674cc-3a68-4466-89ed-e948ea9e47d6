defmodule Drops.Relation.Schema.IntegrationTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema.Integration
  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{PrimaryKey, Field, Indices}

  # Mock schema module for testing
  defmodule MockSchemaModule do
    def schema do
      primary_key = PrimaryKey.new([:id])
      
      fields = [
        Field.new(:id, :integer, :id, :id),
        Field.new(:name, :string, :string, :name),
        Field.new(:email, :string, :string, :email)
      ]

      indices = Indices.new([])

      Schema.new(
        "users",
        primary_key,
        [],  # foreign_keys
        fields,
        indices,
        [],  # associations
        []   # virtual_fields
      )
    end
  end

  # Invalid schema module for testing
  defmodule InvalidSchemaModule do
    def schema do
      "not a schema struct"
    end
  end

  # Module without schema function
  defmodule NoSchemaModule do
    def some_other_function, do: :ok
  end

  describe "load_schema/2" do
    test "loads schema from valid schema module" do
      schema = Integration.load_schema(MockSchemaModule)

      assert %Schema{} = schema
      assert schema.source == "users"
      assert schema.primary_key.fields == [:id]
      assert length(schema.fields) == 3
    end

    test "loads schema with additional associations" do
      additional_associations = [
        # This would be compiled association structs in real usage
      ]

      schema = Integration.load_schema(MockSchemaModule, additional_associations)

      assert %Schema{} = schema
      assert schema.source == "users"
      # In real implementation, associations would be merged
      assert schema.associations == []
    end
  end

  describe "validate_schema_module/1" do
    test "validates valid schema module" do
      assert :ok = Integration.validate_schema_module(MockSchemaModule)
    end

    test "returns error for module not found" do
      assert {:error, :module_not_found} = Integration.validate_schema_module(NonExistentModule)
    end

    test "returns error for module without schema function" do
      assert {:error, :missing_schema_function} = Integration.validate_schema_module(NoSchemaModule)
    end

    test "returns error for invalid schema return type" do
      assert {:error, :invalid_schema_return_type} = Integration.validate_schema_module(InvalidSchemaModule)
    end

    test "returns error when schema function raises" do
      defmodule ErrorSchemaModule do
        def schema do
          raise "Something went wrong"
        end
      end

      assert {:error, {:schema_function_error, %RuntimeError{}}} = 
               Integration.validate_schema_module(ErrorSchemaModule)
    end
  end

  describe "generate_field_definitions/1" do
    test "generates field definitions for Ecto schema" do
      fields = [
        Field.new(:id, :integer, :id, :id),
        Field.new(:name, :string, :string, :name),
        Field.new(:email, :string, :string, :email)
      ]

      definitions = Integration.generate_field_definitions(fields)

      assert length(definitions) == 3

      # Check that each definition is a field AST
      Enum.each(definitions, fn definition ->
        assert match?({:field, _, [_, _]}, definition)
      end)
    end
  end

  describe "generate_association_definitions/1" do
    test "generates belongs_to association definition" do
      belongs_to = %Ecto.Association.BelongsTo{
        field: :user,
        owner: MyApp.Post,
        related: MyApp.User,
        owner_key: :user_id,
        related_key: :id
      }

      definitions = Integration.generate_association_definitions([belongs_to])

      assert length(definitions) == 1
      definition = List.first(definitions)
      assert match?({:belongs_to, _, [:user, MyApp.User]}, definition)
    end

    test "generates has_one association definition" do
      has_one = %Ecto.Association.Has{
        field: :profile,
        owner: MyApp.User,
        related: MyApp.Profile,
        cardinality: :one,
        owner_key: :id,
        related_key: :user_id
      }

      definitions = Integration.generate_association_definitions([has_one])

      assert length(definitions) == 1
      definition = List.first(definitions)
      assert match?({:has_one, _, [:profile, MyApp.Profile]}, definition)
    end

    test "generates has_many association definition" do
      has_many = %Ecto.Association.Has{
        field: :posts,
        owner: MyApp.User,
        related: MyApp.Post,
        cardinality: :many,
        owner_key: :id,
        related_key: :user_id
      }

      definitions = Integration.generate_association_definitions([has_many])

      assert length(definitions) == 1
      definition = List.first(definitions)
      assert match?({:has_many, _, [:posts, MyApp.Post]}, definition)
    end

    test "generates many_to_many association definition" do
      many_to_many = %Ecto.Association.ManyToMany{
        field: :tags,
        owner: MyApp.Post,
        related: MyApp.Tag,
        join_through: "posts_tags",
        owner_key: :id,
        related_key: :id,
        join_keys: [:post_id, :tag_id]
      }

      definitions = Integration.generate_association_definitions([many_to_many])

      assert length(definitions) == 1
      definition = List.first(definitions)
      assert match?({:many_to_many, _, [:tags, MyApp.Tag | _]}, definition)
    end

    test "handles empty associations list" do
      definitions = Integration.generate_association_definitions([])
      assert definitions == []
    end

    test "skips unknown association types" do
      unknown_association = %{field: :unknown, type: :unknown}
      definitions = Integration.generate_association_definitions([unknown_association])

      assert length(definitions) == 1
      # Should generate an empty quote block for unknown types
      assert match?({:__block__, _, []}, List.first(definitions))
    end
  end

  describe "schema_to_ecto_ast/1" do
    test "converts Drops.Relation.Schema to Ecto schema AST" do
      schema = MockSchemaModule.schema()
      ast = Integration.schema_to_ecto_ast(schema)

      # Check that it's a schema definition
      assert match?({:schema, _, ["users", _]}, ast)

      # Extract the schema body
      {_, _, [_table_name, {:__block__, _, body}]} = ast

      # Check that field definitions are present
      field_definitions = Enum.filter(body, fn
        {:field, _, _} -> true
        _ -> false
      end)

      assert length(field_definitions) == 3
    end
  end

  describe "caching functionality" do
    test "get_cached_schema caches and retrieves schemas" do
      # First call should load and cache
      schema1 = Integration.get_cached_schema(TestModule, MockSchemaModule, [])
      assert %Schema{} = schema1

      # Second call should retrieve from cache (same reference)
      schema2 = Integration.get_cached_schema(TestModule, MockSchemaModule, [])
      assert schema1 == schema2
    end

    test "different cache keys produce different results" do
      schema1 = Integration.get_cached_schema(TestModule1, MockSchemaModule, [])
      schema2 = Integration.get_cached_schema(TestModule2, MockSchemaModule, [])

      # Should be equal in content but potentially different cache entries
      assert schema1.source == schema2.source
    end
  end

  describe "integration macro usage" do
    test "using macro sets up module attributes" do
      # This test would require more complex setup to test the macro properly
      # For now, we'll test that the macro can be called without errors
      
      quoted = quote do
        use Drops.Relation.Schema.Integration,
          schema_module: MockSchemaModule,
          repo: MyApp.Repo
      end

      # Should not raise an error when expanded
      assert is_tuple(quoted)
      assert elem(quoted, 0) == :use
    end
  end
end
