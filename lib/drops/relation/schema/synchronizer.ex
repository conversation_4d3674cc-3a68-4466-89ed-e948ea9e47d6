defmodule Drops.Relation.Schema.Synchronizer do
  @moduledoc """
  Handles synchronization of existing schema files with database changes.

  This module provides different strategies for updating existing schema files
  when the database schema changes, including overwrite, merge, and conflict
  resolution approaches.

  ## Synchronization Strategies

  ### Overwrite Strategy
  - Completely replaces existing schema files with new generated content
  - Simple but loses any manual customizations
  - Best for initial generation or when manual changes are minimal

  ### Merge Strategy  
  - Attempts to merge database changes with existing manual customizations
  - Preserves associations and virtual fields added manually
  - Updates field definitions, primary keys, and indices from database
  - More complex but preserves user modifications

  ### Interactive Strategy
  - Shows differences and prompts user for resolution
  - Allows selective application of changes
  - Best for complex scenarios with significant manual customizations

  ## Usage

      # Synchronize with overwrite strategy
      Synchronizer.synchronize_schemas(schemas, existing_files, :overwrite)

      # Synchronize with merge strategy
      Synchronizer.synchronize_schemas(schemas, existing_files, :merge)

      # Interactive synchronization
      Synchronizer.synchronize_schemas(schemas, existing_files, :interactive)
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{FileGenerator, FileOrganizer}

  @type sync_strategy :: :overwrite | :merge | :interactive | :skip_existing
  @type sync_result :: :updated | :skipped | :created | :conflict
  @type file_sync_result :: %{
          file_path: String.t(),
          table_name: String.t(),
          result: sync_result(),
          changes: [String.t()],
          conflicts: [String.t()]
        }

  @type sync_options :: %{
          strategy: sync_strategy(),
          backup_existing: boolean(),
          preserve_associations: boolean(),
          preserve_virtual_fields: boolean(),
          preserve_comments: boolean()
        }

  @doc """
  Synchronizes schema files with database changes.

  ## Parameters

  - `schemas` - List of current database schemas
  - `existing_organizations` - List of existing file organizations
  - `strategy` - Synchronization strategy to use
  - `opts` - Additional synchronization options

  ## Returns

  Returns `{:ok, [file_sync_result]}` with synchronization results.

  ## Examples

      iex> Synchronizer.synchronize_schemas(schemas, existing_files, :merge)
      {:ok, [
        %{file_path: "lib/schemas/users.ex", result: :updated, changes: ["Added new field: :email_verified"], ...},
        %{file_path: "lib/schemas/posts.ex", result: :skipped, changes: [], ...}
      ]}
  """
  @spec synchronize_schemas([Schema.t()], [FileOrganizer.file_organization()], sync_strategy(), keyword()) ::
          {:ok, [file_sync_result()]} | {:error, term()}
  def synchronize_schemas(schemas, existing_organizations, strategy, opts \\ []) do
    options = build_sync_options(strategy, opts)

    results =
      Enum.map(schemas, fn schema ->
        case find_existing_organization(schema.source, existing_organizations) do
          nil ->
            # New schema file - create it
            create_new_schema_file(schema, options)

          existing_org ->
            # Existing schema file - synchronize it
            synchronize_existing_file(schema, existing_org, options)
        end
      end)

    {:ok, results}
  end

  @doc """
  Synchronizes a single schema file.

  ## Parameters

  - `schema` - The current database schema
  - `file_path` - Path to the existing schema file
  - `strategy` - Synchronization strategy
  - `opts` - Additional options

  ## Returns

  Returns a file sync result.
  """
  @spec synchronize_file(Schema.t(), String.t(), sync_strategy(), keyword()) :: file_sync_result()
  def synchronize_file(schema, file_path, strategy, opts \\ []) do
    options = build_sync_options(strategy, opts)

    if File.exists?(file_path) do
      existing_org = %{
        table_name: schema.source,
        file_path: file_path,
        module_name: extract_module_name_from_file(file_path)
      }

      synchronize_existing_file(schema, existing_org, options)
    else
      create_new_schema_file(schema, options)
    end
  end

  @doc """
  Detects changes between current database schema and existing file.

  ## Parameters

  - `current_schema` - Current database schema
  - `existing_file_path` - Path to existing schema file

  ## Returns

  Returns a list of detected changes.

  ## Examples

      iex> Synchronizer.detect_changes(schema, "lib/schemas/users.ex")
      [
        "Added field: :email_verified (boolean)",
        "Removed field: :legacy_id",
        "Modified index: idx_users_email (added uniqueness)"
      ]
  """
  @spec detect_changes(Schema.t(), String.t()) :: [String.t()]
  def detect_changes(current_schema, existing_file_path) do
    case parse_existing_schema(existing_file_path) do
      {:ok, existing_schema} ->
        compare_schemas(existing_schema, current_schema)

      {:error, _} ->
        ["Unable to parse existing schema file"]
    end
  end

  @doc """
  Creates a backup of an existing schema file.

  ## Examples

      iex> Synchronizer.backup_file("lib/schemas/users.ex")
      {:ok, "lib/schemas/users.ex.backup.20231201_143022"}
  """
  @spec backup_file(String.t()) :: {:ok, String.t()} | {:error, term()}
  def backup_file(file_path) do
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601(:basic) |> String.replace(":", "")
    backup_path = "#{file_path}.backup.#{timestamp}"

    case File.copy(file_path, backup_path) do
      {:ok, _} -> {:ok, backup_path}
      {:error, reason} -> {:error, reason}
    end
  end

  # Private helper functions

  defp build_sync_options(strategy, opts) do
    %{
      strategy: strategy,
      backup_existing: Keyword.get(opts, :backup_existing, true),
      preserve_associations: Keyword.get(opts, :preserve_associations, true),
      preserve_virtual_fields: Keyword.get(opts, :preserve_virtual_fields, true),
      preserve_comments: Keyword.get(opts, :preserve_comments, true)
    }
  end

  defp find_existing_organization(table_name, organizations) do
    Enum.find(organizations, fn org ->
      org.table_name == table_name
    end)
  end

  defp create_new_schema_file(schema, options) do
    # This is a new file, so we just generate it normally
    module_name = FileOrganizer.get_module_name(schema.source, "MyApp.Schemas")
    
    case FileGenerator.generate_schema_file(schema, module_name) do
      {:ok, content} ->
        file_path = FileOrganizer.get_file_path(schema.source, "MyApp.Schemas", "lib")
        
        %{
          file_path: file_path,
          table_name: schema.source,
          result: :created,
          changes: ["Created new schema file"],
          conflicts: []
        }

      {:error, _} ->
        %{
          file_path: "",
          table_name: schema.source,
          result: :conflict,
          changes: [],
          conflicts: ["Failed to generate schema file"]
        }
    end
  end

  defp synchronize_existing_file(schema, existing_org, options) do
    case options.strategy do
      :overwrite ->
        synchronize_with_overwrite(schema, existing_org, options)

      :merge ->
        synchronize_with_merge(schema, existing_org, options)

      :interactive ->
        synchronize_with_interaction(schema, existing_org, options)

      :skip_existing ->
        %{
          file_path: existing_org.file_path,
          table_name: schema.source,
          result: :skipped,
          changes: [],
          conflicts: []
        }
    end
  end

  defp synchronize_with_overwrite(schema, existing_org, options) do
    # Create backup if requested
    backup_result = 
      if options.backup_existing do
        backup_file(existing_org.file_path)
      else
        {:ok, nil}
      end

    case backup_result do
      {:ok, _backup_path} ->
        # Generate new content and overwrite
        case FileGenerator.generate_schema_file(schema, existing_org.module_name) do
          {:ok, _content} ->
            %{
              file_path: existing_org.file_path,
              table_name: schema.source,
              result: :updated,
              changes: ["Overwrote existing file with current database schema"],
              conflicts: []
            }

          {:error, _} ->
            %{
              file_path: existing_org.file_path,
              table_name: schema.source,
              result: :conflict,
              changes: [],
              conflicts: ["Failed to generate new schema content"]
            }
        end

      {:error, _} ->
        %{
          file_path: existing_org.file_path,
          table_name: schema.source,
          result: :conflict,
          changes: [],
          conflicts: ["Failed to create backup"]
        }
    end
  end

  defp synchronize_with_merge(schema, existing_org, options) do
    case parse_existing_schema(existing_org.file_path) do
      {:ok, existing_schema} ->
        # Detect changes
        changes = compare_schemas(existing_schema, schema)
        
        if Enum.empty?(changes) do
          %{
            file_path: existing_org.file_path,
            table_name: schema.source,
            result: :skipped,
            changes: [],
            conflicts: []
          }
        else
          # Perform merge
          perform_merge(schema, existing_schema, existing_org, options, changes)
        end

      {:error, _} ->
        # Fall back to overwrite if we can't parse existing file
        synchronize_with_overwrite(schema, existing_org, options)
    end
  end

  defp synchronize_with_interaction(schema, existing_org, options) do
    # For now, fall back to merge strategy
    # In a full implementation, this would prompt the user for decisions
    synchronize_with_merge(schema, existing_org, options)
  end

  defp parse_existing_schema(file_path) do
    # This is a simplified parser - in a full implementation,
    # we would parse the AST to extract the existing schema definition
    case File.read(file_path) do
      {:ok, content} ->
        # For now, return a placeholder schema
        # In reality, we'd parse the file content to extract the schema
        {:ok, %Schema{source: "placeholder", fields: [], primary_key: nil, foreign_keys: [], indices: nil, associations: [], virtual_fields: []}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp compare_schemas(existing_schema, current_schema) do
    changes = []

    # Compare fields
    changes = changes ++ compare_fields(existing_schema.fields, current_schema.fields)

    # Compare primary keys
    changes = changes ++ compare_primary_keys(existing_schema.primary_key, current_schema.primary_key)

    # Compare foreign keys
    changes = changes ++ compare_foreign_keys(existing_schema.foreign_keys, current_schema.foreign_keys)

    # Compare indices
    changes = changes ++ compare_indices(existing_schema.indices, current_schema.indices)

    changes
  end

  defp compare_fields(existing_fields, current_fields) do
    # Simplified field comparison
    existing_names = Enum.map(existing_fields, & &1.name)
    current_names = Enum.map(current_fields, & &1.name)

    added = current_names -- existing_names
    removed = existing_names -- current_names

    changes = []
    changes = changes ++ Enum.map(added, &"Added field: #{inspect(&1)}")
    changes = changes ++ Enum.map(removed, &"Removed field: #{inspect(&1)}")

    changes
  end

  defp compare_primary_keys(existing_pk, current_pk) do
    if existing_pk != current_pk do
      ["Primary key changed"]
    else
      []
    end
  end

  defp compare_foreign_keys(existing_fks, current_fks) do
    if existing_fks != current_fks do
      ["Foreign keys changed"]
    else
      []
    end
  end

  defp compare_indices(existing_indices, current_indices) do
    if existing_indices != current_indices do
      ["Indices changed"]
    else
      []
    end
  end

  defp perform_merge(current_schema, existing_schema, existing_org, options, changes) do
    # Create backup if requested
    backup_result = 
      if options.backup_existing do
        backup_file(existing_org.file_path)
      else
        {:ok, nil}
      end

    case backup_result do
      {:ok, _backup_path} ->
        # Generate merged content
        case generate_merged_content(current_schema, existing_schema, existing_org, options) do
          {:ok, _content} ->
            %{
              file_path: existing_org.file_path,
              table_name: current_schema.source,
              result: :updated,
              changes: changes,
              conflicts: []
            }

          {:error, conflicts} ->
            %{
              file_path: existing_org.file_path,
              table_name: current_schema.source,
              result: :conflict,
              changes: changes,
              conflicts: conflicts
            }
        end

      {:error, _} ->
        %{
          file_path: existing_org.file_path,
          table_name: current_schema.source,
          result: :conflict,
          changes: [],
          conflicts: ["Failed to create backup"]
        }
    end
  end

  defp generate_merged_content(current_schema, existing_schema, existing_org, options) do
    # Merge the schemas preserving user customizations
    merged_schema = %{
      current_schema |
      associations: if(options.preserve_associations, do: existing_schema.associations, else: []),
      virtual_fields: if(options.preserve_virtual_fields, do: existing_schema.virtual_fields, else: [])
    }

    FileGenerator.generate_schema_file(merged_schema, existing_org.module_name)
  end

  defp extract_module_name_from_file(file_path) do
    # Extract module name from file path
    # This is a simplified implementation
    file_path
    |> Path.basename(".ex")
    |> Macro.camelize()
  end
end
