defmodule Drops.Relation.Schema.FileGenerator do
  @moduledoc """
  Generates Elixir files containing explicit Drops.Relation.Schema definitions.

  This module creates standalone schema definition files from database introspection
  metadata. The generated files contain explicit field definitions that users can
  edit and maintain as an alternative to automatic schema inference.

  ## Usage

      # Generate a single schema file
      {:ok, content} = FileGenerator.generate_schema_file(schema, "MyApp.Schemas.Users")

      # Generate schema files for multiple tables
      {:ok, files} = FileGenerator.generate_schema_files(schemas, "MyApp.Schemas")

  ## Generated File Structure

  The generated files follow this structure:

      defmodule MyApp.Schemas.Users do
        @moduledoc \"\"\"
        Explicit schema definition for the 'users' table.

        This file was generated automatically from database introspection.
        You can edit this file to customize field definitions and add associations.
        \"\"\"

        alias Drops.Relation.Schema
        alias Drops.Relation.Schema.{PrimaryKey, Field, Indices, Index}

        @doc \"\"\"
        Returns the explicit schema definition for the 'users' table.
        \"\"\"
        def schema do
          Schema.new(
            "users",
            primary_key(),
            foreign_keys(),
            fields(),
            indices(),
            associations(),
            virtual_fields()
          )
        end

        # Field definitions, primary key, etc.
        ...
      end
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{PrimaryKey, ForeignKey, Field, Indices, Index}

  @type generation_options :: %{
          module_prefix: String.t(),
          output_dir: String.t(),
          include_comments: boolean(),
          include_associations_placeholder: boolean()
        }

  @type file_generation_result :: %{
          module_name: String.t(),
          file_path: String.t(),
          content: String.t(),
          table_name: String.t()
        }

  @doc """
  Generates an Elixir file content for a single schema.

  ## Parameters

  - `schema` - The Drops.Relation.Schema struct
  - `module_name` - The full module name for the generated file
  - `opts` - Generation options

  ## Returns

  Returns `{:ok, content}` with the generated file content.

  ## Examples

      iex> schema = %Drops.Relation.Schema{source: "users", ...}
      iex> FileGenerator.generate_schema_file(schema, "MyApp.Schemas.Users")
      {:ok, "defmodule MyApp.Schemas.Users do\\n..."}
  """
  @spec generate_schema_file(Schema.t(), String.t(), keyword()) :: {:ok, String.t()}
  def generate_schema_file(schema, module_name, opts \\ []) do
    options = build_generation_options(opts)

    content = """
    defmodule #{module_name} do
      @moduledoc \"\"\"
      Explicit schema definition for the '#{schema.source}' table.

      This file was generated automatically from database introspection.
      You can edit this file to customize field definitions and add associations.

      ## Usage

          # Get the schema
          schema = #{module_name}.schema()

          # Use with Drops.Relation
          defmodule MyApp.#{Macro.camelize(schema.source)} do
            use Drops.Relation, schema: #{module_name}.schema()
          end
      \"\"\"

      alias Drops.Relation.Schema
      alias Drops.Relation.Schema.{PrimaryKey, ForeignKey, Field, Indices, Index}

      @doc \"\"\"
      Returns the explicit schema definition for the '#{schema.source}' table.
      \"\"\"
      @spec schema() :: Schema.t()
      def schema do
        Schema.new(
          "#{schema.source}",
          primary_key(),
          foreign_keys(),
          fields(),
          indices(),
          associations(),
          virtual_fields()
        )
      end

      @doc \"\"\"
      Example usage with Drops.Relation.

      ## Direct Usage

          defmodule MyApp.#{Macro.camelize(schema.source)} do
            use Drops.Relation, schema: #{module_name}.schema()
          end

      ## Integration Usage

          defmodule MyApp.#{Macro.camelize(schema.source)} do
            use Drops.Relation.Schema.Integration,
              schema_module: #{module_name},
              repo: MyApp.Repo
          end

      ## With Additional Associations

          defmodule MyApp.#{Macro.camelize(schema.source)} do
            use Drops.Relation.Schema.Integration,
              schema_module: #{module_name},
              repo: MyApp.Repo

            associations do
              # Add your associations here
              # belongs_to :user, MyApp.User
              # has_many :items, MyApp.Item
            end
          end
      \"\"\"
      def usage_examples, do: :see_moduledoc

    #{generate_primary_key_function(schema.primary_key, options)}

    #{generate_foreign_keys_function(schema.foreign_keys, options)}

    #{generate_fields_function(schema.fields, options)}

    #{generate_indices_function(schema.indices, options)}

    #{generate_associations_function(options)}

    #{generate_virtual_fields_function(options)}
    end
    """

    {:ok, content}
  end

  @doc """
  Generates schema files for multiple schemas.

  ## Parameters

  - `schemas` - List of Drops.Relation.Schema structs
  - `module_prefix` - Base module prefix (e.g., "MyApp.Schemas")
  - `opts` - Generation options

  ## Returns

  Returns `{:ok, [file_generation_result]}` with information about generated files.

  ## Examples

      iex> schemas = [%Schema{source: "users"}, %Schema{source: "posts"}]
      iex> FileGenerator.generate_schema_files(schemas, "MyApp.Schemas")
      {:ok, [
        %{module_name: "MyApp.Schemas.Users", file_path: "lib/my_app/schemas/users.ex", ...},
        %{module_name: "MyApp.Schemas.Posts", file_path: "lib/my_app/schemas/posts.ex", ...}
      ]}
  """
  @spec generate_schema_files([Schema.t()], String.t(), keyword()) ::
          {:ok, [file_generation_result()]}
  def generate_schema_files(schemas, module_prefix, opts \\ []) do
    options = build_generation_options(opts)

    results =
      Enum.map(schemas, fn schema ->
        module_name = build_module_name(module_prefix, schema.source)
        file_path = build_file_path(module_name, options.output_dir)

        {:ok, content} = generate_schema_file(schema, module_name, opts)

        %{
          module_name: module_name,
          file_path: file_path,
          content: content,
          table_name: schema.source
        }
      end)

    {:ok, results}
  end

  @doc """
  Builds a module name from a prefix and table name.

  ## Examples

      iex> FileGenerator.build_module_name("MyApp.Schemas", "user_profiles")
      "MyApp.Schemas.UserProfiles"
  """
  @spec build_module_name(String.t(), String.t()) :: String.t()
  def build_module_name(prefix, table_name) do
    module_suffix =
      table_name
      |> String.split("_")
      |> Enum.map(&Macro.camelize/1)
      |> Enum.join("")

    "#{prefix}.#{module_suffix}"
  end

  @doc """
  Builds a file path from a module name and output directory.

  ## Examples

      iex> FileGenerator.build_file_path("MyApp.Schemas.Users", "lib")
      "lib/my_app/schemas/users.ex"
  """
  @spec build_file_path(String.t(), String.t()) :: String.t()
  def build_file_path(module_name, output_dir) do
    # Convert module name to file path
    path_parts =
      module_name
      |> String.split(".")
      |> Enum.map(&Macro.underscore/1)

    file_name = List.last(path_parts) <> ".ex"
    dir_parts = Enum.drop(path_parts, -1)

    Path.join([output_dir] ++ dir_parts ++ [file_name])
  end

  # Private helper functions

  defp build_generation_options(opts) do
    %{
      module_prefix: Keyword.get(opts, :module_prefix, ""),
      output_dir: Keyword.get(opts, :output_dir, "lib"),
      include_comments: Keyword.get(opts, :include_comments, true),
      include_associations_placeholder:
        Keyword.get(opts, :include_associations_placeholder, true)
    }
  end

  defp generate_primary_key_function(primary_key, options) do
    comment =
      if options.include_comments do
        "  # Primary key definition\n"
      else
        ""
      end

    fields_list =
      primary_key.fields
      |> Enum.map(&inspect/1)
      |> Enum.join(", ")

    """
    #{comment}  @doc \"\"\"
      Returns the primary key definition.
      \"\"\"
      @spec primary_key() :: PrimaryKey.t()
      defp primary_key do
        PrimaryKey.new([#{fields_list}])
      end
    """
  end

  defp generate_foreign_keys_function(foreign_keys, options) do
    comment =
      if options.include_comments do
        "  # Foreign key definitions\n"
      else
        ""
      end

    if Enum.empty?(foreign_keys) do
      """
      #{comment}  @doc \"\"\"
        Returns the foreign key definitions.

        Note: Foreign keys cannot be automatically inferred from database structure alone.
        Add foreign key definitions here manually if needed.
        \"\"\"
        @spec foreign_keys() :: [ForeignKey.t()]
        defp foreign_keys do
          [
            # Example:
            # ForeignKey.new(:user_id, "users", :id),
          ]
        end
      """
    else
      fk_definitions =
        Enum.map(foreign_keys, fn fk ->
          "    ForeignKey.new(#{inspect(fk.field)}, \"#{fk.references_table}\", #{inspect(fk.references_field)})"
        end)
        |> Enum.join(",\n")

      """
      #{comment}  @doc \"\"\"
        Returns the foreign key definitions.
        \"\"\"
        @spec foreign_keys() :: [ForeignKey.t()]
        defp foreign_keys do
          [
      #{fk_definitions}
          ]
        end
      """
    end
  end

  defp generate_fields_function(fields, options) do
    comment =
      if options.include_comments do
        "  # Field definitions\n"
      else
        ""
      end

    field_definitions =
      Enum.map(fields, fn field ->
        "    Field.new(#{inspect(field.name)}, #{inspect(field.type)}, #{inspect(field.ecto_type)}, #{inspect(field.source)})"
      end)
      |> Enum.join(",\n")

    """
    #{comment}  @doc \"\"\"
      Returns the field definitions.

      Each field includes:
      - name: The field name as an atom
      - type: The normalized type
      - ecto_type: The original Ecto type
      - source: The source column name in the database
      \"\"\"
      @spec fields() :: [Field.t()]
      defp fields do
        [
    #{field_definitions}
        ]
      end
    """
  end

  defp generate_indices_function(indices, options) do
    comment =
      if options.include_comments do
        "  # Index definitions\n"
      else
        ""
      end

    if Enum.empty?(indices.indices) do
      """
      #{comment}  @doc \"\"\"
        Returns the index definitions.
        \"\"\"
        @spec indices() :: Indices.t()
        defp indices do
          Indices.new([])
        end
      """
    else
      index_definitions =
        Enum.map(indices.indices, fn index ->
          fields_list =
            index.fields
            |> Enum.map(&inspect/1)
            |> Enum.join(", ")

          "    Index.new(\"#{index.name}\", [#{fields_list}], #{index.unique}, #{inspect(index.type)})"
        end)
        |> Enum.join(",\n")

      """
      #{comment}  @doc \"\"\"
        Returns the index definitions.
        \"\"\"
        @spec indices() :: Indices.t()
        defp indices do
          Indices.new([
      #{index_definitions}
          ])
        end
      """
    end
  end

  defp generate_associations_function(options) do
    comment =
      if options.include_comments do
        "  # Association definitions\n"
      else
        ""
      end

    if options.include_associations_placeholder do
      """
      #{comment}  @doc \"\"\"
        Returns the association definitions.

        Note: Associations cannot be automatically inferred from database structure alone.
        Add association definitions here manually.

        Example associations:

            # belongs_to association
            %Ecto.Association.BelongsTo{
              field: :user,
              owner: MyApp.Post,
              related: MyApp.User,
              owner_key: :user_id,
              related_key: :id
            }
        \"\"\"
        @spec associations() :: [Ecto.Association.t()]
        defp associations do
          [
            # Add your associations here
          ]
        end
      """
    else
      """
      #{comment}  @doc \"\"\"
        Returns the association definitions.
        \"\"\"
        @spec associations() :: [Ecto.Association.t()]
        defp associations do
          []
        end
      """
    end
  end

  defp generate_virtual_fields_function(options) do
    comment =
      if options.include_comments do
        "  # Virtual field definitions\n"
      else
        ""
      end

    """
    #{comment}  @doc \"\"\"
      Returns the virtual field definitions.

      Virtual fields are computed fields that don't exist in the database.
      Add virtual field names here if needed.
      \"\"\"
      @spec virtual_fields() :: [atom()]
      defp virtual_fields do
        [
          # Add virtual field names here, e.g.:
          # :full_name,
          # :age,
        ]
      end
    """
  end
end
