defmodule Drops.Relation.Schema.FileOrganizer do
  @moduledoc """
  Handles file organization and naming strategies for generated schema files.

  This module provides consistent file naming and directory structure for
  generated schema files, ensuring they integrate well with the existing
  Drops.Relation system and follow Elixir conventions.

  ## File Organization Strategies

  ### Default Strategy
  - Module prefix: `MyApp.Schemas`
  - Output directory: `lib/my_app/schemas/`
  - File naming: `table_name.ex` (e.g., `users.ex`, `user_profiles.ex`)
  - Module naming: `MyApp.Schemas.TableName` (e.g., `MyApp.Schemas.Users`)

  ### Custom Strategy
  - Configurable module prefix
  - Configurable output directory
  - Consistent naming conventions

  ## Usage

      # Get file path for a table
      path = FileOrganizer.get_file_path("users", "MyApp.Schemas", "lib")
      # => "lib/my_app/schemas/users.ex"

      # Get module name for a table
      module = FileOrganizer.get_module_name("users", "MyApp.Schemas")
      # => "MyApp.Schemas.Users"

      # Organize multiple schemas
      organization = FileOrganizer.organize_schemas(schemas, opts)
  """

  @type organization_strategy :: :default | :custom
  @type file_organization :: %{
          table_name: String.t(),
          module_name: String.t(),
          file_path: String.t(),
          directory_path: String.t(),
          relative_path: String.t()
        }

  @type organization_options :: %{
          module_prefix: String.t(),
          output_dir: String.t(),
          strategy: organization_strategy(),
          namespace_depth: integer(),
          file_suffix: String.t()
        }

  @doc """
  Organizes schema files according to the specified strategy.

  ## Parameters

  - `schemas` - List of schema structs or table names
  - `opts` - Organization options

  ## Returns

  Returns a list of file organization information.

  ## Examples

      iex> schemas = [%Schema{source: "users"}, %Schema{source: "posts"}]
      iex> FileOrganizer.organize_schemas(schemas, module_prefix: "MyApp.Schemas")
      [
        %{
          table_name: "users",
          module_name: "MyApp.Schemas.Users",
          file_path: "lib/my_app/schemas/users.ex",
          ...
        },
        ...
      ]
  """
  @spec organize_schemas([any()], keyword()) :: [file_organization()]
  def organize_schemas(schemas, opts \\ []) do
    options = build_organization_options(opts)

    Enum.map(schemas, fn schema ->
      table_name = extract_table_name(schema)
      organize_single_schema(table_name, options)
    end)
  end

  @doc """
  Organizes a single schema file.

  ## Parameters

  - `table_name` - The database table name
  - `options` - Organization options

  ## Returns

  Returns file organization information for the table.
  """
  @spec organize_single_schema(String.t(), organization_options()) :: file_organization()
  def organize_single_schema(table_name, options) do
    module_name = get_module_name(table_name, options.module_prefix)
    file_path = get_file_path(table_name, options.module_prefix, options.output_dir)
    directory_path = Path.dirname(file_path)
    relative_path = Path.relative_to(file_path, options.output_dir)

    %{
      table_name: table_name,
      module_name: module_name,
      file_path: file_path,
      directory_path: directory_path,
      relative_path: relative_path
    }
  end

  @doc """
  Gets the module name for a table.

  Converts table names to proper Elixir module names following conventions:
  - Snake case to PascalCase
  - Handles underscores and pluralization
  - Maintains namespace structure

  ## Examples

      iex> FileOrganizer.get_module_name("users", "MyApp.Schemas")
      "MyApp.Schemas.Users"

      iex> FileOrganizer.get_module_name("user_profiles", "MyApp.Database.Schemas")
      "MyApp.Database.Schemas.UserProfiles"

      iex> FileOrganizer.get_module_name("oauth_tokens", "MyApp.Schemas")
      "MyApp.Schemas.OauthTokens"
  """
  @spec get_module_name(String.t(), String.t()) :: String.t()
  def get_module_name(table_name, module_prefix) do
    module_suffix = table_name_to_module_name(table_name)
    "#{module_prefix}.#{module_suffix}"
  end

  @doc """
  Gets the file path for a table.

  Converts module names to proper file paths following Elixir conventions:
  - PascalCase to snake_case
  - Maintains directory structure
  - Adds .ex extension

  ## Examples

      iex> FileOrganizer.get_file_path("users", "MyApp.Schemas", "lib")
      "lib/my_app/schemas/users.ex"

      iex> FileOrganizer.get_file_path("user_profiles", "MyApp.Database.Schemas", "lib")
      "lib/my_app/database/schemas/user_profiles.ex"
  """
  @spec get_file_path(String.t(), String.t(), String.t()) :: String.t()
  def get_file_path(table_name, module_prefix, output_dir) do
    # Convert module prefix to directory path
    dir_parts =
      module_prefix
      |> String.split(".")
      |> Enum.map(&Macro.underscore/1)

    # Use table name as file name
    file_name = "#{table_name}.ex"

    Path.join([output_dir] ++ dir_parts ++ [file_name])
  end

  @doc """
  Gets the directory path for a module prefix.

  ## Examples

      iex> FileOrganizer.get_directory_path("MyApp.Schemas", "lib")
      "lib/my_app/schemas"
  """
  @spec get_directory_path(String.t(), String.t()) :: String.t()
  def get_directory_path(module_prefix, output_dir) do
    dir_parts =
      module_prefix
      |> String.split(".")
      |> Enum.map(&Macro.underscore/1)

    Path.join([output_dir] ++ dir_parts)
  end

  @doc """
  Validates that a file organization is valid.

  Checks for:
  - Valid module names
  - Valid file paths
  - No conflicts with existing Elixir modules
  - Proper directory structure

  ## Examples

      iex> org = %{module_name: "MyApp.Schemas.Users", file_path: "lib/my_app/schemas/users.ex", ...}
      iex> FileOrganizer.validate_organization(org)
      :ok

      iex> org = %{module_name: "Invalid.Module.Name!", ...}
      iex> FileOrganizer.validate_organization(org)
      {:error, :invalid_module_name}
  """
  @spec validate_organization(file_organization()) :: :ok | {:error, term()}
  def validate_organization(organization) do
    with :ok <- validate_module_name(organization.module_name),
         :ok <- validate_file_path(organization.file_path),
         :ok <- validate_directory_structure(organization.directory_path) do
      :ok
    end
  end

  @doc """
  Creates the necessary directory structure for schema files.

  ## Examples

      iex> FileOrganizer.ensure_directory_structure("lib/my_app/schemas")
      :ok
  """
  @spec ensure_directory_structure(String.t()) :: :ok | {:error, term()}
  def ensure_directory_structure(directory_path) do
    case File.mkdir_p(directory_path) do
      :ok -> :ok
      {:error, reason} -> {:error, {:mkdir_failed, reason}}
    end
  end

  @doc """
  Checks if a schema file already exists.

  ## Examples

      iex> FileOrganizer.file_exists?("lib/my_app/schemas/users.ex")
      false
  """
  @spec file_exists?(String.t()) :: boolean()
  def file_exists?(file_path) do
    File.exists?(file_path)
  end

  @doc """
  Gets a list of existing schema files in a directory.

  ## Examples

      iex> FileOrganizer.list_existing_files("lib/my_app/schemas")
      ["users.ex", "posts.ex", "comments.ex"]
  """
  @spec list_existing_files(String.t()) :: [String.t()]
  def list_existing_files(directory_path) do
    case File.ls(directory_path) do
      {:ok, files} ->
        files
        |> Enum.filter(&String.ends_with?(&1, ".ex"))
        |> Enum.sort()

      {:error, _} ->
        []
    end
  end

  # Private helper functions

  defp build_organization_options(opts) do
    %{
      module_prefix: Keyword.get(opts, :module_prefix, "MyApp.Schemas"),
      output_dir: Keyword.get(opts, :output_dir, "lib"),
      strategy: Keyword.get(opts, :strategy, :default),
      namespace_depth: Keyword.get(opts, :namespace_depth, 2),
      file_suffix: Keyword.get(opts, :file_suffix, ".ex")
    }
  end

  defp extract_table_name(%{source: source}), do: source
  defp extract_table_name(table_name) when is_binary(table_name), do: table_name

  defp table_name_to_module_name(table_name) do
    table_name
    |> String.split("_")
    |> Enum.map(&Macro.camelize/1)
    |> Enum.join("")
  end

  defp validate_module_name(module_name) do
    # Check if module name follows Elixir conventions
    if Regex.match?(~r/^[A-Z][a-zA-Z0-9_.]*$/, module_name) do
      :ok
    else
      {:error, :invalid_module_name}
    end
  end

  defp validate_file_path(file_path) do
    # Check if file path is valid
    if String.ends_with?(file_path, ".ex") and not String.contains?(file_path, "..") do
      :ok
    else
      {:error, :invalid_file_path}
    end
  end

  defp validate_directory_structure(directory_path) do
    # Check if directory path is valid
    if not String.contains?(directory_path, "..") do
      :ok
    else
      {:error, :invalid_directory_path}
    end
  end
end
