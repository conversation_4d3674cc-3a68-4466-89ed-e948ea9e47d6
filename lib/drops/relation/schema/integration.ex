defmodule Drops.Relation.Schema.Integration do
  @moduledoc """
  Integration layer for using generated schema files with Drops.Relation.

  This module provides utilities and macros to seamlessly integrate generated
  explicit schema definition files with the existing Drops.Relation system,
  allowing them to be used as an alternative to automatic schema inference.

  ## Usage Patterns

  ### Pattern 1: Direct Schema Usage
  Use a generated schema directly with Drops.Relation:

      defmodule MyApp.Users do
        use Drops.Relation, schema: MyApp.Schemas.Users.schema()
      end

  ### Pattern 2: Schema Module Integration
  Create a relation module that references a generated schema:

      defmodule MyApp.Users do
        use Drops.Relation.Schema.Integration,
          schema_module: MyApp.Schemas.Users,
          repo: MyApp.Repo
      end

  ### Pattern 3: Mixed Approach
  Use generated schema with additional customizations:

      defmodule MyApp.Users do
        use Drops.Relation.Schema.Integration,
          schema_module: MyApp.Schemas.Users,
          repo: MyApp.Repo

        # Add custom associations
        associations do
          has_many :posts, MyApp.Posts, foreign_key: :user_id
        end
      end

  ## Integration Features

  - Seamless compatibility with existing Drops.Relation API
  - Support for schema caching and performance optimization
  - Ability to override or extend generated schemas
  - Integration with Ecto schema generation
  - Support for associations and virtual fields
  """

  @doc """
  Macro for integrating generated schema modules with Drops.Relation.

  This macro provides a convenient way to use generated schema files
  while maintaining compatibility with the existing Drops.Relation system.

  ## Options

  - `:schema_module` - The generated schema module (required)
  - `:repo` - The Ecto repository (optional, for database operations)
  - `:table_name` - Override the table name (optional)
  - `:cache_schema` - Whether to cache the schema (default: true)

  ## Examples

      defmodule MyApp.Users do
        use Drops.Relation.Schema.Integration,
          schema_module: MyApp.Schemas.Users,
          repo: MyApp.Repo
      end
  """
  defmacro __using__(opts) do
    schema_module = Keyword.fetch!(opts, :schema_module)
    repo = Keyword.get(opts, :repo)
    table_name = Keyword.get(opts, :table_name)
    cache_schema = Keyword.get(opts, :cache_schema, true)

    quote do
      use Ecto.Schema

      import Drops.Relation
      import Drops.Relation.Schema.Integration

      @before_compile Drops.Relation.Schema.Integration

      @schema_module unquote(schema_module)
      @repo unquote(repo)
      @table_name unquote(table_name)
      @cache_schema unquote(cache_schema)
      @associations []

      Module.register_attribute(__MODULE__, :associations, accumulate: true)
    end
  end

  @doc """
  Macro for defining additional associations on top of generated schemas.

  This allows users to add associations that couldn't be inferred from
  the database structure.

  ## Examples

      associations do
        has_many :posts, MyApp.Posts, foreign_key: :user_id
        belongs_to :organization, MyApp.Organization
      end
  """
  defmacro associations(do: block) do
    quote do
      @associations unquote(Macro.escape(block))
    end
  end

  defmacro __before_compile__(env) do
    relation = env.module
    schema_module = Module.get_attribute(relation, :schema_module)
    repo = Module.get_attribute(relation, :repo)
    table_name = Module.get_attribute(relation, :table_name)
    cache_schema = Module.get_attribute(relation, :cache_schema)
    additional_associations = Module.get_attribute(relation, :associations, [])

    quote do
      # Generate the Ecto schema from the Drops.Relation.Schema
      unquote(generate_ecto_schema(schema_module, table_name, additional_associations))

      @doc """
      Returns the Drops.Relation.Schema for this relation.

      This schema is loaded from the generated schema module and may be
      cached for performance.

      ## Examples

          iex> MyApp.Users.schema()
          %Drops.Relation.Schema{
            source: "users",
            primary_key: %Drops.Relation.Schema.PrimaryKey{fields: [:id]},
            ...
          }
      """
      @spec schema() :: Drops.Relation.Schema.t()
      def schema do
        if unquote(cache_schema) do
          Drops.Relation.Schema.Integration.get_cached_schema(
            __MODULE__,
            unquote(schema_module),
            unquote(additional_associations)
          )
        else
          Drops.Relation.Schema.Integration.load_schema(
            unquote(schema_module),
            unquote(additional_associations)
          )
        end
      end

      @doc """
      Returns the repository module for this relation.
      """
      @spec repo() :: module() | nil
      def repo, do: unquote(repo)

      @doc """
      Returns the table name for this relation.
      """
      @spec table_name() :: String.t()
      def table_name do
        case unquote(table_name) do
          nil -> unquote(schema_module).schema().source
          name -> name
        end
      end
    end
  end

  @doc """
  Loads a schema from a generated schema module.

  ## Parameters

  - `schema_module` - The generated schema module
  - `additional_associations` - Additional associations to merge

  ## Returns

  Returns the loaded Drops.Relation.Schema.
  """
  @spec load_schema(module(), list()) :: Drops.Relation.Schema.t()
  def load_schema(schema_module, additional_associations \\ []) do
    base_schema = schema_module.schema()

    # Merge additional associations if provided
    if Enum.empty?(additional_associations) do
      base_schema
    else
      compiled_associations = compile_associations(additional_associations)
      %{base_schema | associations: base_schema.associations ++ compiled_associations}
    end
  end

  @doc """
  Gets a cached schema or loads it if not cached.

  ## Parameters

  - `relation_module` - The relation module using the schema
  - `schema_module` - The generated schema module
  - `additional_associations` - Additional associations to merge

  ## Returns

  Returns the cached or loaded Drops.Relation.Schema.
  """
  @spec get_cached_schema(module(), module(), list()) :: Drops.Relation.Schema.t()
  def get_cached_schema(relation_module, schema_module, additional_associations \\ []) do
    cache_key = {relation_module, schema_module, additional_associations}

    case Drops.Relation.SchemaCache.get(cache_key) do
      nil ->
        schema = load_schema(schema_module, additional_associations)
        Drops.Relation.SchemaCache.put(cache_key, schema)
        schema

      cached_schema ->
        cached_schema
    end
  end

  @doc """
  Validates that a generated schema module is compatible.

  ## Parameters

  - `schema_module` - The schema module to validate

  ## Returns

  Returns `:ok` if valid, `{:error, reason}` if invalid.

  ## Examples

      iex> Drops.Relation.Schema.Integration.validate_schema_module(MyApp.Schemas.Users)
      :ok

      iex> Drops.Relation.Schema.Integration.validate_schema_module(InvalidModule)
      {:error, :missing_schema_function}
  """
  @spec validate_schema_module(module()) :: :ok | {:error, term()}
  def validate_schema_module(schema_module) when is_atom(schema_module) do
    cond do
      not Code.ensure_loaded?(schema_module) ->
        {:error, :module_not_found}

      not function_exported?(schema_module, :schema, 0) ->
        {:error, :missing_schema_function}

      true ->
        try do
          schema = schema_module.schema()

          if is_struct(schema, Drops.Relation.Schema) do
            :ok
          else
            {:error, :invalid_schema_return_type}
          end
        rescue
          error ->
            {:error, {:schema_function_error, error}}
        end
    end
  end

  @doc """
  Converts a Drops.Relation.Schema to Ecto schema AST.

  This function generates the Ecto schema definition that corresponds
  to the Drops.Relation.Schema structure.

  ## Parameters

  - `drops_schema` - The Drops.Relation.Schema struct

  ## Returns

  Returns the Ecto schema AST.
  """
  @spec schema_to_ecto_ast(Drops.Relation.Schema.t()) :: Macro.t()
  def schema_to_ecto_ast(drops_schema) do
    field_definitions = generate_field_definitions(drops_schema.fields)
    association_definitions = generate_association_definitions(drops_schema.associations)

    quote do
      schema unquote(drops_schema.source) do
        unquote_splicing(field_definitions)
        unquote_splicing(association_definitions)
      end
    end
  end

  # Private helper functions

  defp generate_ecto_schema(schema_module, table_name_override, additional_associations) do
    quote do
      # Load the schema at compile time to generate Ecto schema
      compile_time_schema = unquote(schema_module).schema()

      # Override table name if provided
      actual_table_name = unquote(table_name_override) || compile_time_schema.source

      # Generate field definitions
      field_definitions = 
        Drops.Relation.Schema.Integration.generate_field_definitions(compile_time_schema.fields)

      # Generate association definitions from the schema
      base_association_definitions = 
        Drops.Relation.Schema.Integration.generate_association_definitions(compile_time_schema.associations)

      # Add additional associations
      additional_association_definitions = unquote(additional_associations)

      # Combine all definitions
      all_association_definitions = base_association_definitions ++ additional_association_definitions

      # Generate the Ecto schema
      schema actual_table_name do
        unquote_splicing(field_definitions)
        unquote_splicing(all_association_definitions)
      end
    end
  end

  @doc false
  def generate_field_definitions(fields) do
    Enum.map(fields, fn field ->
      quote do
        field unquote(field.name), unquote(field.ecto_type)
      end
    end)
  end

  @doc false
  def generate_association_definitions(associations) do
    # Convert Ecto association structs back to AST
    # This is a simplified implementation - in practice, we'd need
    # to handle all association types properly
    Enum.map(associations, fn association ->
      case association do
        %Ecto.Association.BelongsTo{} ->
          quote do
            belongs_to unquote(association.field), unquote(association.related)
          end

        %Ecto.Association.Has{cardinality: :one} ->
          quote do
            has_one unquote(association.field), unquote(association.related)
          end

        %Ecto.Association.Has{cardinality: :many} ->
          quote do
            has_many unquote(association.field), unquote(association.related)
          end

        %Ecto.Association.ManyToMany{} ->
          quote do
            many_to_many unquote(association.field), unquote(association.related),
              join_through: unquote(association.join_through)
          end

        _ ->
          # Skip unknown association types
          quote do
          end
      end
    end)
  end

  defp compile_associations(association_definitions) do
    # This would compile association AST into Ecto association structs
    # For now, return empty list as this is complex to implement properly
    []
  end
end
