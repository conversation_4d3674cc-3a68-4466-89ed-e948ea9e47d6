#!/usr/bin/env elixir

# Explicit Schema Generation Example
# 
# This script demonstrates the complete workflow of using the explicit schema
# generation system as an alternative to automatic schema inference.

# Load the application
Code.require_file("examples/setup.exs")

# Set up the database with test tables
IO.puts("Setting up test database...")

# Create test tables
Drops.TestRepo.query!("""
CREATE TABLE IF NOT EXISTS organizations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
""")

Drops.TestRepo.query!("""
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  email TEXT UNIQUE,
  organization_id INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (organization_id) REFERENCES organizations(id)
)
""")

Drops.TestRepo.query!("""
CREATE TABLE IF NOT EXISTS posts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  content TEXT,
  user_id INTEGER NOT NULL,
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
)
""")

# Create indices
Drops.TestRepo.query!("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
Drops.TestRepo.query!("CREATE INDEX IF NOT EXISTS idx_users_organization ON users(organization_id)")
Drops.TestRepo.query!("CREATE INDEX IF NOT EXISTS idx_posts_user ON posts(user_id)")

IO.puts("✓ Database tables created")

# Insert some test data
IO.puts("Inserting test data...")

Drops.TestRepo.query!("""
INSERT OR IGNORE INTO organizations (id, name, slug) VALUES 
  (1, 'Acme Corp', 'acme-corp'),
  (2, 'Tech Startup', 'tech-startup')
""")

Drops.TestRepo.query!("""
INSERT OR IGNORE INTO users (id, name, email, organization_id) VALUES 
  (1, 'John Doe', '<EMAIL>', 1),
  (2, 'Jane Smith', '<EMAIL>', 1),
  (3, 'Bob Wilson', '<EMAIL>', 2)
""")

Drops.TestRepo.query!("""
INSERT OR IGNORE INTO posts (id, title, content, user_id, published_at) VALUES 
  (1, 'Hello World', 'This is my first post', 1, datetime('now')),
  (2, 'Elixir Tips', 'Some useful Elixir patterns', 2, datetime('now')),
  (3, 'Draft Post', 'This is not published yet', 1, NULL)
""")

IO.puts("✓ Test data inserted")

# Demonstrate database introspection
IO.puts("\n=== Database Introspection ===")

alias Drops.Relation.Schema.DatabaseSchemaIntrospector

# Discover all tables
{:ok, tables} = DatabaseSchemaIntrospector.discover_tables(Drops.TestRepo)
IO.puts("Discovered tables:")
Enum.each(tables, fn table ->
  IO.puts("  - #{table.name} (#{table.type})")
end)

# Extract schema for users table
{:ok, users_schema} = DatabaseSchemaIntrospector.extract_table_schema(Drops.TestRepo, "users")
IO.puts("\nUsers table schema:")
IO.puts("  Source: #{users_schema.source}")
IO.puts("  Primary key: #{inspect(users_schema.primary_key.fields)}")
IO.puts("  Fields: #{length(users_schema.fields)}")
Enum.each(users_schema.fields, fn field ->
  IO.puts("    - #{field.name}: #{field.type}")
end)
IO.puts("  Foreign keys: #{length(users_schema.foreign_keys)}")
Enum.each(users_schema.foreign_keys, fn fk ->
  IO.puts("    - #{fk.field} -> #{fk.references_table}.#{fk.references_field}")
end)

# Demonstrate schema file generation
IO.puts("\n=== Schema File Generation ===")

alias Drops.Relation.Schema.FileGenerator

# Generate schema file for users table
{:ok, users_file_content} = FileGenerator.generate_schema_file(
  users_schema, 
  "Example.Schemas.Users"
)

IO.puts("Generated schema file content (first 500 chars):")
IO.puts(String.slice(users_file_content, 0, 500) <> "...")

# Generate schema files for all tables
{:ok, all_schemas} = DatabaseSchemaIntrospector.extract_all_schemas(Drops.TestRepo)
successful_schemas = Enum.map(all_schemas, & &1.schema) |> Enum.filter(& &1 != nil)

{:ok, file_results} = FileGenerator.generate_schema_files(
  successful_schemas,
  "Example.Schemas"
)

IO.puts("\nGenerated schema files:")
Enum.each(file_results, fn result ->
  IO.puts("  - #{result.module_name} -> #{result.file_path}")
end)

# Demonstrate creating schema modules dynamically
IO.puts("\n=== Dynamic Schema Module Creation ===")

# Create the Users schema module dynamically
users_module_code = """
defmodule Example.Schemas.Users do
  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{PrimaryKey, ForeignKey, Field, Indices, Index}

  def schema do
    Schema.new(
      "users",
      PrimaryKey.new([:id]),
      [ForeignKey.new(:organization_id, "organizations", :id)],
      [
        Field.new(:id, :integer, :integer, :id),
        Field.new(:name, :string, :string, :name),
        Field.new(:email, :string, :string, :email),
        Field.new(:organization_id, :integer, :integer, :organization_id),
        Field.new(:created_at, :naive_datetime, :naive_datetime, :created_at)
      ],
      Indices.new([
        Index.new("idx_users_email", [:email], true, :btree),
        Index.new("idx_users_organization", [:organization_id], false, :btree)
      ]),
      [],  # associations
      []   # virtual_fields
    )
  end
end
"""

# Compile the schema module
[{Example.Schemas.Users, _}] = Code.compile_string(users_module_code)

IO.puts("✓ Created Example.Schemas.Users module")

# Test the schema module
schema = Example.Schemas.Users.schema()
IO.puts("Schema source: #{schema.source}")
IO.puts("Schema fields: #{length(schema.fields)}")

# Demonstrate integration with Drops.Relation
IO.puts("\n=== Integration with Drops.Relation ===")

# Create a relation using the explicit schema
relation_code = """
defmodule Example.Users do
  use Drops.Relation, schema: Example.Schemas.Users.schema()
end
"""

[{Example.Users, _}] = Code.compile_string(relation_code)

IO.puts("✓ Created Example.Users relation")

# Test that the relation works
relation_schema = Example.Users.schema()
IO.puts("Relation schema source: #{relation_schema.source}")
IO.puts("Relation schema fields: #{length(relation_schema.fields)}")

# Demonstrate schema validation
IO.puts("\n=== Schema Validation ===")

alias Drops.Relation.Schema.Integration

case Integration.validate_schema_module(Example.Schemas.Users) do
  :ok -> 
    IO.puts("✓ Schema module validation passed")
  {:error, reason} -> 
    IO.puts("✗ Schema module validation failed: #{inspect(reason)}")
end

# Test loading schema
loaded_schema = Integration.load_schema(Example.Schemas.Users)
IO.puts("✓ Schema loaded successfully")
IO.puts("Loaded schema source: #{loaded_schema.source}")

# Demonstrate file organization
IO.puts("\n=== File Organization ===")

alias Drops.Relation.Schema.FileOrganizer

# Show how file paths are organized
organization = FileOrganizer.organize_single_schema("users", %{
  module_prefix: "MyApp.Schemas",
  output_dir: "lib"
})

IO.puts("File organization for 'users' table:")
IO.puts("  Module name: #{organization.module_name}")
IO.puts("  File path: #{organization.file_path}")
IO.puts("  Directory: #{organization.directory_path}")

# Show multiple table organization
organizations = FileOrganizer.organize_schemas(successful_schemas, 
  module_prefix: "MyApp.Schemas",
  output_dir: "lib"
)

IO.puts("\nAll table organizations:")
Enum.each(organizations, fn org ->
  IO.puts("  #{org.table_name} -> #{org.module_name}")
end)

IO.puts("\n=== Example Complete ===")
IO.puts("This example demonstrated:")
IO.puts("✓ Database introspection and table discovery")
IO.puts("✓ Schema extraction with fields, primary keys, and foreign keys")
IO.puts("✓ Schema file generation with proper Elixir code")
IO.puts("✓ Dynamic module creation and compilation")
IO.puts("✓ Integration with existing Drops.Relation system")
IO.puts("✓ Schema validation and loading")
IO.puts("✓ File organization and naming strategies")
IO.puts("\nThe explicit schema generation system is working correctly!")
