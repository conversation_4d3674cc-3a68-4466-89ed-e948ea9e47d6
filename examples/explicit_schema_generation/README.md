# Explicit Schema Generation Example

This example demonstrates the new explicit schema generation system for Drops.Relation, which provides an alternative to automatic schema inference by generating standalone Elixir files containing explicit schema definitions.

## Overview

The explicit schema generation system consists of several components:

1. **Database Introspection** - Discovers and extracts complete schema information from databases
2. **Schema File Generation** - Creates Elixir files with explicit Drops.Relation.Schema definitions
3. **Mix Task** - Provides `mix drops.gen.schemas` command for easy usage
4. **Integration Layer** - Seamlessly integrates generated schemas with existing Drops.Relation system

## Quick Start

### 1. Generate Schema Files

```bash
# Generate schemas for all tables
mix drops.gen.schemas --repo MyApp.Repo

# Generate schemas for specific tables
mix drops.gen.schemas --repo MyApp.Repo --tables users,posts,comments

# Generate with custom module prefix and output directory
mix drops.gen.schemas --repo MyApp.Repo \
  --module-prefix MyApp.Database.Schemas \
  --output-dir lib/my_app/database/schemas
```

### 2. Use Generated Schemas

#### Option A: Direct Usage
```elixir
defmodule MyApp.Users do
  use Drops.Relation, schema: MyApp.Schemas.Users.schema()
end
```

#### Option B: Integration Usage (Recommended)
```elixir
defmodule MyApp.Users do
  use Drops.Relation.Schema.Integration,
    schema_module: MyApp.Schemas.Users,
    repo: MyApp.Repo
end
```

#### Option C: With Additional Associations
```elixir
defmodule MyApp.Users do
  use Drops.Relation.Schema.Integration,
    schema_module: MyApp.Schemas.Users,
    repo: MyApp.Repo

  associations do
    has_many :posts, MyApp.Posts, foreign_key: :user_id
    has_one :profile, MyApp.Profile, foreign_key: :user_id
    belongs_to :organization, MyApp.Organization
  end
end
```

## Example Database Setup

Let's create a sample database with some tables to demonstrate the system:

```sql
-- Users table
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  email TEXT UNIQUE,
  organization_id INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (organization_id) REFERENCES organizations(id)
);

-- Organizations table
CREATE TABLE organizations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Posts table
CREATE TABLE posts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  content TEXT,
  user_id INTEGER NOT NULL,
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create some indices
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_organization ON users(organization_id);
CREATE INDEX idx_posts_user ON posts(user_id);
CREATE INDEX idx_posts_published ON posts(published_at) WHERE published_at IS NOT NULL;
```

## Generated Schema Files

After running `mix drops.gen.schemas --repo MyApp.Repo`, you'll get files like:

### `lib/my_app/schemas/users.ex`

```elixir
defmodule MyApp.Schemas.Users do
  @moduledoc """
  Explicit schema definition for the 'users' table.

  This file was generated automatically from database introspection.
  You can edit this file to customize field definitions and add associations.
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{PrimaryKey, ForeignKey, Field, Indices, Index}

  @doc """
  Returns the explicit schema definition for the 'users' table.
  """
  @spec schema() :: Schema.t()
  def schema do
    Schema.new(
      "users",
      primary_key(),
      foreign_keys(),
      fields(),
      indices(),
      associations(),
      virtual_fields()
    )
  end

  # Primary key definition
  @spec primary_key() :: PrimaryKey.t()
  defp primary_key do
    PrimaryKey.new([:id])
  end

  # Foreign key definitions
  @spec foreign_keys() :: [ForeignKey.t()]
  defp foreign_keys do
    [
      ForeignKey.new(:organization_id, "organizations", :id)
    ]
  end

  # Field definitions
  @spec fields() :: [Field.t()]
  defp fields do
    [
      Field.new(:id, :integer, :integer, :id),
      Field.new(:name, :string, :string, :name),
      Field.new(:email, :string, :string, :email),
      Field.new(:organization_id, :integer, :integer, :organization_id),
      Field.new(:created_at, :naive_datetime, :naive_datetime, :created_at)
    ]
  end

  # Index definitions
  @spec indices() :: Indices.t()
  defp indices do
    Indices.new([
      Index.new("idx_users_email", [:email], true, :btree),
      Index.new("idx_users_organization", [:organization_id], false, :btree)
    ])
  end

  # Association definitions
  @spec associations() :: [Ecto.Association.t()]
  defp associations do
    [
      # Add your associations here manually
      # Example:
      # %Ecto.Association.BelongsTo{
      #   field: :organization,
      #   owner: MyApp.User,
      #   related: MyApp.Organization,
      #   owner_key: :organization_id,
      #   related_key: :id
      # }
    ]
  end

  # Virtual field definitions
  @spec virtual_fields() :: [atom()]
  defp virtual_fields do
    [
      # Add virtual field names here, e.g.:
      # :full_name,
      # :display_name,
    ]
  end
end
```

## Usage Examples

### Basic Relation Definition

```elixir
defmodule MyApp.Users do
  use Drops.Relation.Schema.Integration,
    schema_module: MyApp.Schemas.Users,
    repo: MyApp.Repo
end

# Now you can use it like any Drops.Relation
users = MyApp.Users.all()
user = MyApp.Users.find(1)
```

### Adding Associations

```elixir
defmodule MyApp.Users do
  use Drops.Relation.Schema.Integration,
    schema_module: MyApp.Schemas.Users,
    repo: MyApp.Repo

  associations do
    belongs_to :organization, MyApp.Organizations
    has_many :posts, MyApp.Posts, foreign_key: :user_id
    has_one :profile, MyApp.Profiles, foreign_key: :user_id
  end
end

defmodule MyApp.Posts do
  use Drops.Relation.Schema.Integration,
    schema_module: MyApp.Schemas.Posts,
    repo: MyApp.Repo

  associations do
    belongs_to :user, MyApp.Users
    has_many :comments, MyApp.Comments, foreign_key: :post_id
  end
end
```

### Customizing Generated Schemas

You can edit the generated schema files to add custom logic:

```elixir
defmodule MyApp.Schemas.Users do
  # ... generated content ...

  # Add custom virtual fields
  defp virtual_fields do
    [:full_name, :display_name]
  end

  # Add custom associations
  defp associations do
    [
      %Ecto.Association.BelongsTo{
        field: :organization,
        owner: MyApp.User,
        related: MyApp.Organization,
        owner_key: :organization_id,
        related_key: :id
      }
    ]
  end
end
```

## Schema Synchronization

When your database schema changes, you can update the generated files:

```bash
# Update existing schema files
mix drops.gen.schemas --repo MyApp.Repo --update

# See what would change without updating
mix drops.gen.schemas --repo MyApp.Repo --update --dry-run
```

## Benefits

1. **Explicit Control** - Full control over schema definitions
2. **Version Control Friendly** - Schema changes are visible in diffs
3. **Customizable** - Easy to add associations and virtual fields
4. **Performance** - No runtime introspection overhead
5. **Documentation** - Self-documenting schema files
6. **Migration Safe** - Schemas persist through application restarts

## Migration from Automatic Inference

To migrate from automatic inference to explicit schemas:

1. Generate schema files for your existing tables
2. Update your relation modules to use the generated schemas
3. Add any custom associations or virtual fields
4. Test thoroughly to ensure compatibility
5. Remove automatic inference configuration

## Best Practices

1. **Version Control** - Always commit generated schema files
2. **Review Changes** - Review generated schemas before committing
3. **Custom Logic** - Add associations and virtual fields manually
4. **Synchronization** - Regularly sync schemas after migrations
5. **Testing** - Test schema changes thoroughly
6. **Documentation** - Document custom modifications in schema files

## Troubleshooting

### Common Issues

1. **Module Not Found** - Ensure schema modules are compiled
2. **Invalid Schema** - Check that schema functions return valid Schema structs
3. **Association Errors** - Verify association definitions are correct
4. **Cache Issues** - Restart application if using schema caching

### Debugging

```elixir
# Validate a schema module
Drops.Relation.Schema.Integration.validate_schema_module(MyApp.Schemas.Users)

# Load schema without caching
Drops.Relation.Schema.Integration.load_schema(MyApp.Schemas.Users)

# Check schema content
MyApp.Schemas.Users.schema() |> IO.inspect()
```

## Complete Working Example

See `examples/explicit_schema_generation/example.exs` for a complete working example that demonstrates:

- Setting up a test database
- Generating schema files
- Using generated schemas with Drops.Relation
- Adding custom associations
- Querying data using the explicit schemas
